import time
import datetime
import hashlib
import logging
from functools import lru_cache
from typing import Dict, <PERSON>, Tuple, Literal

import stripe

from django.db.models import QuerySet

from mainapp.decorators import dynamic_lru_cache
from AbunDRFBackend.settings import REDIS_STRIPE_DB
from mainapp.models import BackLink, User, AutoCoupon, AppSumoLicense

logger = logging.getLogger('abun.frontend.pages')


def get_plan_features(plan_data: Dict) -> List[str]:
    """
    Returns the features for a given plan
    :param plan_data: Plan data
    :return: List of features
    """
    # If the plan is not to be shown, return empty list
    if plan_data['metadata']['show'].lower() == "false":
        return []

    # Get the plan features
    blog_automation = int(plan_data['metadata']['max_blog_automation']) == 9999 and \
        "Unlimited" or int(plan_data['metadata']['max_blog_automation'])

    content_calendar = int(plan_data['metadata']['max_content_calendar']) == 9999 and \
        "Unlimited" or int(plan_data['metadata']['max_content_calendar'])

    search_console_insights = int(plan_data['metadata']['max_search_console_insights']) == 9999 and \
        "Unlimited" or int(plan_data['metadata']['max_search_console_insights'])

    fast_indexing = int(plan_data['metadata']['max_fast_indexing']) == 9999 and \
        "Unlimited" or int(plan_data['metadata']['max_fast_indexing'])

    backlink_directory = plan_data['name'].lower() != "trial" and \
        "Unlimited" or BackLink.objects.filter(show_on_free_plan=True).count()

    ai_calculator_widgets = int(plan_data['metadata']['max_ai_calculators']) == 9999 and \
        "Unlimited" or int(plan_data['metadata']['max_ai_calculators'])

    ai_statistics_pages = int(plan_data['metadata']['max_ai_calculators']) == 9999 and \
        "Unlimited" or int(plan_data['metadata']['max_ai_calculators'])

    ai_comparison_pages = int(plan_data['metadata']['max_ai_calculators']) == 9999 and \
        "Unlimited" or int(plan_data['metadata']['max_ai_calculators'])

    guest_post_finder = int(plan_data['metadata']['max_guest_post_finder_queries']) == 9999 and \
        "Unlimited" or int(plan_data['metadata']['max_guest_post_finder_queries'])

    reddit_seo = int(plan_data['metadata']['max_reddit_post_finder_queries']) == 9999 and \
        "Unlimited" or int(plan_data['metadata']['max_reddit_post_finder_queries'])

    ai_auto_schema = int(plan_data['metadata']['max_ai_auto_schema']) == 99999 and \
        "Unlimited" or f"{plan_data['metadata']['max_ai_auto_schema']} URLs"

    # New plans
    features = {
        'trial': [
            { 'label': "Blog Automation", 'value': blog_automation },
            { 'label': "Content Calender", 'value': content_calendar },
            { 'label': "Search Console Insights", 'value': search_console_insights },
            { 'label': "Fast Indexing", 'value': fast_indexing },
            { 'label': "Backlink Directory", 'value': backlink_directory },
            { 'label': "AI Calculator Widgets", 'value': ai_calculator_widgets },
            { 'label': "AI Statistics Pages", 'value': ai_statistics_pages },
            { 'label': "AI Comparison Pages", 'value': ai_comparison_pages },
            { 'label': "Guest Post Finder", 'value': guest_post_finder },
            { 'label': "Reddit SEO", 'value': reddit_seo },
            { 'label': "AI Auto Schema", 'value': ai_auto_schema }
        ],

        'seed': [
            { 'label': "Blog Automation", 'value': blog_automation },
            { 'label': "Content Calender", 'value': content_calendar },
            { 'label': "Search Console Insights", 'value': search_console_insights },
            { 'label': "Fast Indexing", 'value': fast_indexing },
            { 'label': "Backlink Directory", 'value': backlink_directory },
            { 'label': "AI Calculator Widgets", 'value': ai_calculator_widgets },
            { 'label': "AI Statistics Pages", 'value': ai_statistics_pages },
            { 'label': "AI Comparison Pages", 'value': ai_comparison_pages },
            { 'label': "Guest Post Finder", 'value': guest_post_finder },
            { 'label': "Reddit SEO", 'value': reddit_seo },
            { 'label': "AI Auto Schema", 'value': ai_auto_schema }
        ],

        'starter': [
            { 'label': "Blog Automation", 'value': blog_automation },
            { 'label': "Content Calender", 'value': content_calendar },
            { 'label': "Search Console Insights", 'value': search_console_insights },
            { 'label': "Fast Indexing", 'value': fast_indexing },
            { 'label': "Backlink Directory", 'value': backlink_directory },
            { 'label': "AI Calculator Widgets", 'value': ai_calculator_widgets },
            { 'label': "AI Statistics Pages", 'value': ai_statistics_pages },
            { 'label': "AI Comparison Pages", 'value': ai_comparison_pages },
            { 'label': "Guest Post Finder", 'value': guest_post_finder },
            { 'label': "Reddit SEO", 'value': reddit_seo },
            { 'label': "AI Auto Schema", 'value': ai_auto_schema }
        ],

        'growth': [
            { 'label': "Blog Automation", 'value': blog_automation },
            { 'label': "Content Calender", 'value': content_calendar },
            { 'label': "Search Console Insights", 'value': search_console_insights },
            { 'label': "Fast Indexing", 'value': fast_indexing },
            { 'label': "Backlink Directory", 'value': backlink_directory },
            { 'label': "AI Calculator Widgets", 'value': ai_calculator_widgets },
            { 'label': "AI Statistics Pages", 'value': ai_statistics_pages },
            { 'label': "AI Comparison Pages", 'value': ai_comparison_pages },
            { 'label': "Guest Post Finder", 'value': guest_post_finder },
            { 'label': "Reddit SEO", 'value': reddit_seo },
            { 'label': "AI Auto Schema", 'value': ai_auto_schema }
        ],

        'growth max': [
            { 'label': "Blog Automation", 'value': blog_automation },
            { 'label': "Content Calender", 'value': content_calendar },
            { 'label': "Search Console Insights", 'value': search_console_insights },
            { 'label': "Fast Indexing", 'value': fast_indexing },
            { 'label': "Backlink Directory", 'value': backlink_directory },
            { 'label': "AI Calculator Widgets", 'value': ai_calculator_widgets },
            { 'label': "AI Statistics Pages", 'value': ai_statistics_pages },
            { 'label': "AI Comparison Pages", 'value': ai_comparison_pages },
            { 'label': "Guest Post Finder", 'value': guest_post_finder },
            { 'label': "Reddit SEO", 'value': reddit_seo },
            { 'label': "AI Auto Schema", 'value': ai_auto_schema }
        ],
    }

    return features.get(plan_data['name'].lower(), [])


def get_plan_tools(plan_data: Dict) -> List[str]:
    """
    Returns the tools for a given plan
    :param plan_data: Plan data
    :return: List of tools
    """
    # If the plan is not to be shown, return empty list
    if plan_data['metadata']['show'].lower() == "false":
        return []

    # Get the AI Glossary Creator limit
    ai_glossary_creator = int(plan_data['metadata']['max_glossary_topics']) == 9999 and \
        "Unlimited" or int(plan_data['metadata']['max_glossary_topics'])

    # New plans
    tools = {
        'trial': [
            { 'label': "AI Glossary Creator", 'value': ai_glossary_creator },
            { 'label': "AI Programmatic SEO" },
            { 'label': "LongTail Keyword Research" },
            { 'label': "Steal Competitor Keywords" },
            { 'label': "AI Keyword Research" },
            { 'label': "ICP to KW Research" },
            { 'label': "Manual Keyword Research" },
            { 'label': "Video to Article", 'comingSoon': True },
            { 'label': "AI Article Updater", 'comingSoon': True },
            { 'label': "AI Auto Technical SEO", 'comingSoon': True },
            { 'label': "AI Internal Links Builder", 'comingSoon': True },
            { 'label': "GMB SEO Tracker", 'comingSoon': True },
            { 'label': "AI SEO Tracker", 'comingSoon': True }
        ],

        'seed': [
            { 'label': "AI Glossary Creator", 'value': ai_glossary_creator },
            { 'label': "AI Programmatic SEO" },
            { 'label': "LongTail Keyword Research" },
            { 'label': "Steal Competitor Keywords" },
            { 'label': "AI Keyword Research" },
            { 'label': "ICP to KW Research" },
            { 'label': "Manual Keyword Research" },
            { 'label': "Video to Article", 'comingSoon': True },
            { 'label': "AI Article Updater", 'comingSoon': True },
            { 'label': "AI Auto Technical SEO", 'comingSoon': True },
            { 'label': "AI Internal Links Builder", 'comingSoon': True },
            { 'label': "GMB SEO Tracker", 'comingSoon': True },
            { 'label': "AI SEO Tracker", 'comingSoon': True }
        ],

        'starter': [
            { 'label': "AI Glossary Creator", 'value': ai_glossary_creator },
            { 'label': "AI Programmatic SEO" },
            { 'label': "LongTail Keyword Research" },
            { 'label': "Steal Competitor Keywords" },
            { 'label': "AI Keyword Research" },
            { 'label': "ICP to KW Research" },
            { 'label': "Manual Keyword Research" },
            { 'label': "Video to Article", 'comingSoon': True },
            { 'label': "AI Article Updater", 'comingSoon': True },
            { 'label': "AI Auto Technical SEO", 'comingSoon': True },
            { 'label': "AI Internal Links Builder", 'comingSoon': True },
            { 'label': "GMB SEO Tracker", 'comingSoon': True },
            { 'label': "AI SEO Tracker", 'comingSoon': True }
        ],

        'growth': [
            { 'label': "AI Glossary Creator", 'value': ai_glossary_creator },
            { 'label': "AI Programmatic SEO" },
            { 'label': "LongTail Keyword Research" },
            { 'label': "Steal Competitor Keywords" },
            { 'label': "AI Keyword Research" },
            { 'label': "ICP to KW Research" },
            { 'label': "Manual Keyword Research" },
            { 'label': "Video to Article", 'comingSoon': True },
            { 'label': "AI Article Updater", 'comingSoon': True },
            { 'label': "AI Auto Technical SEO", 'comingSoon': True },
            { 'label': "AI Internal Links Builder", 'comingSoon': True },
            { 'label': "GMB SEO Tracker", 'comingSoon': True },
            { 'label': "AI SEO Tracker", 'comingSoon': True }
        ],

        'growth max': [
            { 'label': "AI Glossary Creator", 'value': ai_glossary_creator },
            { 'label': "AI Programmatic SEO" },
            { 'label': "LongTail Keyword Research" },
            { 'label': "Steal Competitor Keywords" },
            { 'label': "AI Keyword Research" },
            { 'label': "ICP to KW Research" },
            { 'label': "Manual Keyword Research" },
            { 'label': "Video to Article", 'comingSoon': True },
            { 'label': "AI Article Updater", 'comingSoon': True },
            { 'label': "AI Auto Technical SEO", 'comingSoon': True },
            { 'label': "AI Internal Links Builder", 'comingSoon': True },
            { 'label': "GMB SEO Tracker", 'comingSoon': True },
            { 'label': "AI SEO Tracker", 'comingSoon': True }
        ],
    }

    return tools.get(plan_data['name'].lower(), [])


def get_all_product_data(user: User, active=True, is_sorted=True) -> List[Dict]:
    """
    Returns all stripe product data with following datapoints:
    - id: string
    - name: string
    - metadata: Metadata
    - price_id: string
    - price_amount: int
    - currency_code: string ("usd"/"inr")

    Metadata:
    - description: string
    - position: int
    - max_articles: int
    - max_titles: int
    - websites: int
    - show: boolean
    - popular: boolean

    NOTE: products with metadata 'show'

    :param user: User model object.
    :param active: Only consider those prices that are active.
    :param is_sorted: Sort the data based on 'position' metadata
    :return: Stripe products data
    """
    plans: List[Dict] = []
    currency_code: str = 'inr' if user.country == "India" else 'usd'

    for price in stripe.Price.list(active=active, expand=['data.product']).auto_paging_iter():
        try:
            if (price['product']['metadata']['show'] == "true") and (price['currency'] == currency_code):
                metadata: Dict = price['product']['metadata'].to_dict()
                plans.append({
                    'id': price['product']['id'],
                    'name': price['product']['name'],
                    'articles': int(metadata['max_articles']),
                    'credits': int(metadata['max_keywords']),
                    'sites': int(metadata['max_websites']) == 9999 and "Unlimited" or int(metadata['max_websites']),
                    'annual_plan': price.lookup_key and 'annual' in price.lookup_key.lower() or False,
                    'metadata': {
                        'position': int(metadata['position']),
                        'description': metadata['description'],
                        'show': metadata['show'] == "true",
                        'popular': metadata['popular'] == "true",
                        'max_articles': int(metadata['max_articles']),
                        'max_titles': int(metadata['max_titles']),
                        'max_websites': int(metadata['max_websites']),
                        'max_blog_automation': int(metadata['max_blog_automation']),
                        'max_keywords': int(metadata['max_keywords']),
                        'max_glossary_topics': int(metadata['max_glossary_topics']),
                        'max_glossary_words': int(metadata['max_glossary_words']),
                        'max_ai_calculators': int(metadata['max_ai_calculators']),
                        'max_blog_finder_emails': int(metadata['max_blog_finder_emails']),
                        'max_reddit_post_finder_queries': int(metadata['max_reddit_post_finder_queries']),
                        'max_guest_post_finder_queries': int(metadata['max_guest_post_finder_queries']),
                        'max_ai_auto_schema': int(metadata['max_ai_auto_schema']),
                        'max_content_calendar': int(metadata['max_content_calendar']),
                        'max_search_console_insights': int(metadata['max_search_console_insights']),
                        'max_fast_indexing': int(metadata['max_fast_indexing']),
                    },
                    'price_id': price['id'],
                    'price_amount': price['unit_amount'],
                    'currency_code': currency_code,

                    'features': get_plan_features(price['product']),
                    'tools': get_plan_tools(price['product']),
                })
        except KeyError as k:
            logger.error(f"get_all_product_data() - Missing metadata {k} while fetching all product data")

    if is_sorted and plans:
        plans.sort(key=lambda p: p['metadata']['position'])

    return plans


def save_idempotency_key(idempotency_key: str):
    """
    Saves given idempotency key to Redis for 3 days.
    """
    # Importing here to avoid circular import
    from mainapp.utils import get_redis_connection

    with get_redis_connection(db=REDIS_STRIPE_DB) as redis_connection:
        redis_connection.set(idempotency_key, 1)
        redis_connection.expire(idempotency_key, 259200)


def generate_stripe_event_idempotency_key(usage: str, user_email: str, username: str) -> str:
    """
    Generates idempotency key for stripe events. Can be used in cases where we need to create some resource manually
    and want to use the idempotency key to prevent webhook code from running. The key is stored in Redis and expires
    after 1 hour.

    Ex. If we need to create stripe customer manually from our end instead of stripe creating one for us, we would be
    getting and saving stripe customer id in the same code/function. We don't want webhook to save the details
    again. So we check for this idempotency key on the webhook event and ignore the event if it matches.

    :param usage: Some string defining the event it's going to be used in.
    :param user_email: User email id.
    :param username: User account name.
    :returns: Idempotency key
    """
    h = hashlib.sha256()
    h.update(usage.encode('utf-8'))
    h.update(user_email.encode('utf-8'))
    h.update(username.encode('utf-8'))
    h.update(str(datetime.datetime.now().timestamp()).encode('utf-8'))
    idempotency_key = "abun_manual_event_" + h.hexdigest()

    save_idempotency_key(idempotency_key)

    return idempotency_key


def check_stripe_event_idempotency_key(idempotency_key: str) -> bool:
    """
    Checks if given idempotency_key is present in Redis. If so returns True
    """
    # Importing here to avoid circular import
    from mainapp.utils import get_redis_connection

    with get_redis_connection(db=REDIS_STRIPE_DB) as redis_connection:
        value = redis_connection.get(idempotency_key)

    return bool(value)


def get_stripe_product_data_by_name(plan_name: str) -> Dict:
    """
    Retrieves product data {id, name, metadata, inr, usd} from Stripe based on product name. The name is case-insensitive.

    Metadata contains all the different quotas or max limits for user's account for this plan.
    Metadata includes 'position', 'websites', 'max_articles' & 'max_titles'

    inr and usd contains 'id' & 'amount'.

    :param plan_name: Stripe product name (case-insensitive)
    :returns: Product data - product id (str), metadata (dict), inr pricing (int) & used pricing (int)
    """
    products = stripe.Product.list(active=True)
    try:
        product = list(filter(lambda p: p['name'].lower() == plan_name.lower(), products))[0]
    except IndexError:
        raise Exception(f"No plan with name {plan_name} was found")

    metadata = product['metadata'].to_dict()
    product_data = {
        'id': product['id'],
        'name': product['name'],
        'metadata': {
            'position': int(metadata['position']),
            'description': metadata['description'],
            'show': metadata['show'] == "true",
            'popular': metadata['popular'] == "true",
            'max_articles': int(metadata['max_articles']),
            'max_titles': int(metadata['max_titles']),
            'max_websites': int(metadata['max_websites']),
            'max_blog_automation': int(metadata['max_blog_automation']),
            'max_keywords': int(metadata['max_keywords']),
            'max_glossary_topics': int(metadata['max_glossary_topics']),
            'max_glossary_words': int(metadata['max_glossary_words']),
            'max_ai_calculators': int(metadata['max_ai_calculators']),
            'max_blog_finder_emails': int(metadata['max_blog_finder_emails']),
            'max_reddit_post_finder_queries': int(metadata['max_reddit_post_finder_queries']),
            'max_guest_post_finder_queries': int(metadata['max_guest_post_finder_queries']),
            'max_ai_auto_schema': int(metadata['max_ai_auto_schema']),
            'max_content_calendar': int(metadata['max_content_calendar']),
            'max_search_console_insights': int(metadata['max_search_console_insights']),
            'max_fast_indexing': int(metadata['max_fast_indexing']),
        }
    }
    for price in stripe.Price.list(product=product, expand=['data.currency_options']):
        product_data.update({
            f"{price['currency']}": {
                "id": price['id'],
                "amount": price['unit_amount']
            }
        })

    return product_data


def get_stripe_product_data_by_id(product_id: str):
    """
    Retrieves product data {id, name, metadata, inr, usd} from Stripe based on product id.

    Metadata contains all the different quotas or max limits for user's account for this plan.
    Metadata includes 'position', 'websites', 'max_articles' & 'max_titles'.

    inr and usd contains 'id' & 'amount'.

    :param product_id: Stripe product id.
    :returns: Product data - product id (str), name (str), metadata (dict), inr (dict) & usd (dict)
    """
    product = stripe.Product.retrieve(product_id)
    metadata = product['metadata'].to_dict()
    product_data = {
        'id': product['id'],
        'name': product['name'],
        'metadata': {
            'position': int(metadata['position']),
            'description': metadata['description'],
            'show': metadata['show'] == "true",
            'popular': metadata['popular'] == "true",
            'max_articles': int(metadata['max_articles']),
            'max_titles': int(metadata['max_titles']),
            'max_websites': int(metadata['max_websites']),
            'max_blog_automation': int(metadata['max_blog_automation']),
            'max_keywords': int(metadata['max_keywords']),
            'max_glossary_topics': int(metadata['max_glossary_topics']),
            'max_glossary_words': int(metadata['max_glossary_words']),
            'max_ai_calculators': int(metadata['max_ai_calculators']),
            'max_blog_finder_emails': int(metadata['max_blog_finder_emails']),
            'max_reddit_post_finder_queries': int(metadata['max_reddit_post_finder_queries']),
            'max_guest_post_finder_queries': int(metadata['max_guest_post_finder_queries']),
            'max_ai_auto_schema': int(metadata['max_ai_auto_schema']),
            'max_content_calendar': int(metadata['max_content_calendar']),
            'max_search_console_insights': int(metadata['max_search_console_insights']),
            'max_fast_indexing': int(metadata['max_fast_indexing']),
        }
    }
    for price in stripe.Price.list(product=product, expand=['data.currency_options']):
        product_data.update({
            f"{price['currency']}": {
                "id": price['id'],
                "amount": price['unit_amount']
            }
        })

    return product_data


def get_stripe_product_data(user: User):
    """
    Retrieves product data {id, name, metadata, inr, usd} from Stripe based on product id.

    Metadata contains all the different quotas or max limits for user's account for this plan.
    Metadata includes 'position', 'websites', 'max_articles' & 'max_titles'.

    inr and usd contains 'id' & 'amount'.

    :param user: User model instance
    :returns: Product data - product id (str), name (str), metadata (dict), inr (dict) & usd (dict)
    """
    def get_ltd_plans_combined_limit(appsumo_licenses: QuerySet[AppSumoLicense],
                                     limit_for: Literal["website", "keyword", "title",
                                                        "article", "ai_calculator",
                                                        "ai_auto_schema", "glossary_topic",
                                                        "glossary_word", "blog_finder_email",
                                                        "reddit_post_finder_query", "guest_post_finder_query",
                                                        "blog_automation", "content_calendar",
                                                        "search_console_insights", "fast_indexing"]):
        """
        Returns LTD plan combined limit for `websites`, `keywords`, `titles`, `articles`, and `calculators`.
        :param user: User model instance
        :param limit_for: Limit for `websites`, `keyword`, `title`, `article`, and `ai_calculator` etc.
        """
        # Get the combined limit
        ltd_total_limit = sum(getattr(apppsumo_license, f"{limit_for}_limit") for apppsumo_license in appsumo_licenses.all())
        return ltd_total_limit

    try:
        # Fetch the product        
        if not user.stripe_product_id:
            raise ValueError("Stripe Product ID is missing for the user.")
        
        product = stripe.Product.retrieve(user.stripe_product_id)
    except stripe.error.InvalidRequestError as err:
        logger.error(f"Error fetching product: {err}")

        # Try fetching the product again after 5 seconds
        time.sleep(5)
        product = stripe.Product.retrieve(user.stripe_product_id)

    metadata = product['metadata'].to_dict()

    # Fetch active lts plans
    appsumo_licenses = user.active_ltd_plans

    if appsumo_licenses.exists():
        product_data = {
            'id': "ltd",
            'name': "LTD",
            'display_name': f"LTD and {product['name']}",
            'metadata': {
                'position': 99,
                'description': "LTD and " + metadata['description'],
                'show': True,
                'popular': False,

                # Get combined limit from LTD plans and add the limit from the subscription plan if it's not a trial plan
                'max_articles': get_ltd_plans_combined_limit(appsumo_licenses, "article") + \
                                    (product['name'] != "Trial" and int(metadata['max_articles']) or 0),

                'max_titles': get_ltd_plans_combined_limit(appsumo_licenses, "title") + \
                                (product['name'] != "Trial" and int(metadata['max_titles'])),

                'max_websites': get_ltd_plans_combined_limit(appsumo_licenses, "website") + \
                                (product['name'] != "Trial" and int(metadata['max_websites'])),

                'max_blog_automation': get_ltd_plans_combined_limit(appsumo_licenses, "blog_automation") + \
                                        (product['name'] != "Trial" and int(metadata['max_blog_automation'])),

                'max_keywords': get_ltd_plans_combined_limit(appsumo_licenses, "keyword") + \
                                   ( product['name'] != "Trial" and int(metadata['max_keywords'])),

                'max_glossary_topics': get_ltd_plans_combined_limit(appsumo_licenses, "glossary_topic") + \
                                        (product['name'] != "Trial" and int(metadata['max_glossary_topics'])),

                'max_glossary_words': get_ltd_plans_combined_limit(appsumo_licenses, "glossary_word") + \
                                        (product['name'] != "Trial" and int(metadata['max_glossary_words'])),

                'max_ai_calculators': get_ltd_plans_combined_limit(appsumo_licenses, "ai_calculator") + \
                                        (product['name'] != "Trial" and int(metadata['max_ai_calculators'])),

                'max_blog_finder_emails': get_ltd_plans_combined_limit(appsumo_licenses, "blog_finder_email") + \
                                            (product['name'] != "Trial" and int(metadata['max_blog_finder_emails'])),

                'max_reddit_post_finder_queries': get_ltd_plans_combined_limit(appsumo_licenses, "reddit_post_finder_query") + \
                                                    (product['name'] != "Trial" and int(metadata['max_reddit_post_finder_queries'])),

                'max_guest_post_finder_queries': get_ltd_plans_combined_limit(appsumo_licenses, "guest_post_finder_query") + \
                                                    (product['name'] != "Trial" and int(metadata['max_guest_post_finder_queries'])),

                'max_ai_auto_schema': get_ltd_plans_combined_limit(appsumo_licenses, "ai_auto_schema") + \
                                        (product['name'] != "Trial" and int(metadata['max_ai_auto_schema'])),

                'max_content_calendar': get_ltd_plans_combined_limit(appsumo_licenses, "content_calendar") + \
                                        (product['name'] != "Trial" and int(metadata['max_content_calendar'])),

                'max_search_console_insights': get_ltd_plans_combined_limit(appsumo_licenses, "search_console_insights") + \
                                               (product['name'] != "Trial" and int(metadata['max_search_console_insights'])),

                'max_fast_indexing': get_ltd_plans_combined_limit(appsumo_licenses, "fast_indexing") + \
                                     (product['name'] != "Trial" and int(metadata['max_fast_indexing'])),
            }
        }

    else:
        product_data = {
            'id': product['id'],
            'name': product['name'],
            'display_name': product['name'],
            'metadata': {
                'position': int(metadata['position']),
                'description': metadata['description'],
                'show': metadata['show'] == "true",
                'popular': metadata['popular'] == "true",
                'max_articles': int(metadata['max_articles']),
                'max_titles': int(metadata['max_titles']),
                'max_websites': int(metadata['max_websites']),
                'max_blog_automation': int(metadata['max_blog_automation']),
                'max_keywords': int(metadata['max_keywords']),
                'max_glossary_topics': int(metadata['max_glossary_topics']),
                'max_glossary_words': int(metadata['max_glossary_words']),
                'max_ai_calculators': int(metadata['max_ai_calculators']),
                'max_blog_finder_emails': int(metadata['max_blog_finder_emails']),
                'max_reddit_post_finder_queries': int(metadata['max_reddit_post_finder_queries']),
                'max_guest_post_finder_queries': int(metadata['max_guest_post_finder_queries']),
                'max_ai_auto_schema': int(metadata['max_ai_auto_schema']),
                'max_content_calendar': int(metadata['max_content_calendar']),
                'max_search_console_insights': int(metadata['max_search_console_insights']),
                'max_fast_indexing': int(metadata['max_fast_indexing']),
            }
        }

    for price in stripe.Price.list(product=product, expand=['data.currency_options']):
        product_data.update({
            f"{price['currency']}": {
                "id": price['id'],
                "amount": price['unit_amount']
            }
        })

    return product_data


def latest_invoice_is_open(stripe_customer_id: str) -> bool:
    """
    Checks if latest invoice has 'open' status. If so returns True

    :param stripe_customer_id:
    :return:
    """
    invoices = stripe.Invoice.list(customer=stripe_customer_id)

    try:
        return invoices['data'][0]['status'] == 'open'
    except IndexError:
        return False


def create_checkout_session(user: User, price_id: str, success_url: str, cancel_url: str) -> str:
    """
    Starts a new checkout session, saves session id to user model and returns session url.

    Uses existing stripe customer id if present. Otherwise, if it's None, stripe will create a new customer
    with user's email id.

    :param user: User model object.
    :param price_id: Stripe price id.
    :param success_url: Stripe will redirect users here after successful payment.
    :param cancel_url: Stripe will redirect users here if they use the checkout back button.
    :returns: checkout session url.
    """
    try:
        coupon = AutoCoupon.objects.filter(enabled=True)
        discount_code = coupon[0].coupon_code
    except Exception as err:
        logger.error("No discount_code found--", err)
        discount_code = ""

    if user.stripe_customer_id:
        if discount_code:
            stripe_session = stripe.checkout.Session.create(
                line_items=[{
                    "price": price_id,
                    "quantity": 1
                }],
                mode="subscription",
                discounts= [{
                    "coupon": discount_code, 
                }],
                success_url=success_url,
                cancel_url=cancel_url,
                customer=user.stripe_customer_id,
                payment_method_types=['card'],
                billing_address_collection='required',
            )
        else:
            stripe_session = stripe.checkout.Session.create(
                line_items=[{
                    "price": price_id,
                    "quantity": 1
                }],
                mode="subscription",
                success_url=success_url,
                cancel_url=cancel_url,
                allow_promotion_codes=True,
                customer=user.stripe_customer_id,
                payment_method_types=['card'],
                billing_address_collection='required',
            )
    else:
        if discount_code:
            stripe_session = stripe.checkout.Session.create(
                line_items=[{
                    "price": price_id,
                    "quantity": 1
                }],
                mode="subscription",
                discounts= [{
                    "coupon": discount_code, 
                }],
                success_url=success_url,
                cancel_url=cancel_url,
                customer_email=user.email,
                payment_method_types=['card'],
                billing_address_collection='required',
            )
        else:
            stripe_session = stripe.checkout.Session.create(
                line_items=[{
                    "price": price_id,
                    "quantity": 1
                }],
                mode="subscription",
                success_url=success_url,
                cancel_url=cancel_url,
                allow_promotion_codes=True,
                customer_email=user.email,
                payment_method_types=['card'],
                billing_address_collection='required',
            )

    user.stripe_active_checkout_session_id = stripe_session['id']
    user.save()

    # Mark as first-time purchase in Redis
    mark_as_first_time_purchase(user.email)

    return stripe_session['url']


@dynamic_lru_cache()
def fetch_all_stripe_products_id_and_name(limit: int = 100) -> Tuple[List, List]:
    """
    Fetches all stripe products id and name
    :param limit: No. of products to fetch in a single request (optional)
    """
    products = []
    last_customer_id = None
    has_more = True

    while has_more:
        response = stripe.Product.list(
            limit=limit,
            starting_after=last_customer_id
        )

        products.extend(response.data)
        has_more = response.has_more

        if has_more:
            last_customer_id = response.data[-1].id

    products_id = []
    products_name = []

    for product in products:
        products_id.append(product.id)
        products_name.append(product.name)

    return products_id, products_name


def fetch_all_stripe_products(limit: int = 100) -> List:
    """
    Fetches all stripe products
    :param limit: No. of products to fetch in a single request (optional)
    """
    products = []
    last_customer_id = None
    has_more = True

    while has_more:
        response = stripe.Product.list(
            limit=limit,
            starting_after=last_customer_id
        )

        products.extend(response.data)
        has_more = response.has_more

        if has_more:
            last_customer_id = response.data[-1].id

    products_dict = []

    for product in products:
        metadata = product['metadata'].to_dict()
        product_data = {
            'id': product['id'],
            'name': product['name'],
            'metadata': {
                'position': int(metadata['position']),
                'description': metadata['description'],
                'show': metadata['show'] == "true",
                'popular': metadata['popular'] == "true",
                'max_articles': int(metadata['max_articles']),
                'max_titles': int(metadata['max_titles']),
                'max_websites': int(metadata['max_websites']),
                'max_blog_automation': int(metadata['max_blog_automation']),
                'max_keywords': int(metadata['max_keywords']),
                'max_glossary_topics': int(metadata['max_glossary_topics']),
                'max_glossary_words': int(metadata['max_glossary_words']),
                'max_ai_calculators': int(metadata['max_ai_calculators']),
                'max_blog_finder_emails': int(metadata['max_blog_finder_emails']),
                'max_reddit_post_finder_queries': int(metadata['max_reddit_post_finder_queries']),
                'max_guest_post_finder_queries': int(metadata['max_guest_post_finder_queries']),
                'max_ai_auto_schema': int(metadata['max_ai_auto_schema']),
                'max_content_calendar': int(metadata['max_content_calendar']),
                'max_search_console_insights': int(metadata['max_search_console_insights']),
                'max_fast_indexing': int(metadata['max_fast_indexing']),
            }
        }

        products_dict.append(product_data)

    return products_dict


@lru_cache(typed=True, maxsize=50)
def get_user_plan_name_by_product_id(product_id: str):
    """
    Returns the user plan name by product id
    :paran product_id: 
    """
    if not product_id:
        return "Not Found"

    products_id, products_name = fetch_all_stripe_products_id_and_name()

    try:
        return products_name[products_id.index(product_id)]
    except (IndexError, ValueError):
        return "Not Found"


def mark_as_first_time_purchase(email: str, expiry_seconds: int = 259200):
    """
    Mark a user as making their first purchase attempt with a Redis key.
    Keys expire after 3 days (259200 seconds) by default.
    
    :param email: User's email address
    :param expiry_seconds: Time in seconds until the key expires
    """
    # Importing here to avoid circular import
    from mainapp.utils import get_redis_connection

    redis_key = f"first_time_purchase:{email}"
    with get_redis_connection(db=REDIS_STRIPE_DB) as redis_connection:
        redis_connection.set(redis_key, 1)
        redis_connection.expire(redis_key, expiry_seconds)


def is_first_time_purchase(email: str) -> bool:
    """
    Check if this is the user's first purchase attempt.
    
    :param email: User's email address
    :return: True if this is the first purchase, False otherwise
    """
    # Importing here to avoid circular import
    from mainapp.utils import get_redis_connection

    redis_key = f"first_time_purchase:{email}"
    with get_redis_connection(db=REDIS_STRIPE_DB) as redis_connection:
        return bool(redis_connection.get(redis_key))


def clear_first_time_purchase_flag(email: str):
    """
    Clear the first-time purchase flag for a user.
    
    :param email: User's email address
    """
    # Importing here to avoid circular import
    from mainapp.utils import get_redis_connection

    redis_key = f"first_time_purchase:{email}"
    with get_redis_connection(db=REDIS_STRIPE_DB) as redis_connection:
        redis_connection.delete(redis_key)


def cancel_trial_subscription_if_needed(user: User, user_prev_product_id: str, user_prev_sub_id: str, trial_plan: Dict):
    """
    Cancel the user's trial subscription if they're upgrading from a trial plan on their first purchase.
    
    :param user: User model object
    :param user_prev_product_id: Previous product ID
    :param user_prev_sub_id: Previous subscription ID
    :param trial_plan: Trial plan data
    """
    if user_prev_product_id != trial_plan['id']:
        return  # Only proceed if previous plan was a trial

    if not is_first_time_purchase(user.email):
        return  # Only proceed if this is their first purchase

    # Generate idempotency key for cancellation
    free_plan_cancellation_ik = generate_stripe_event_idempotency_key(
        "free_plan_cancellation", user.email, user.username
    )

    try:
        # Retrieve and cancel the previous subscription
        prev_sub = stripe.Subscription.retrieve(user_prev_sub_id)

        if prev_sub['status'] != 'canceled':
            stripe.Subscription.delete(user_prev_sub_id, idempotency_key=free_plan_cancellation_ik)
            logger.info(f"Canceled trial subscription {user_prev_sub_id} for user {user.email}")
        else:
            logger.info(f"Trial subscription {user_prev_sub_id} is already canceled.")

    except stripe.error.InvalidRequestError:
        logger.error(f"Failed to cancel trial subscription: No such subscription '{user_prev_sub_id}'")

    except Exception as e:
        logger.critical(f"Unexpected error canceling trial subscription: {str(e)}")

    # Clear the first-time purchase flag
    clear_first_time_purchase_flag(user.email)


def get_user_plan_names_batch(user_product_ids: List[str]) -> Dict[str, str]:
    """
    Returns a dictionary mapping product_ids to plan names
    :param user_product_ids: List of stripe product IDs
    :return: Dictionary mapping product_ids to plan names
    """
    # Filter out None values
    valid_product_ids = [pid for pid in user_product_ids if pid]
    
    if not valid_product_ids:
        return {}
    
    products_id, products_name = fetch_all_stripe_products_id_and_name()
    
    # Create mapping of product_id to name
    product_name_map = {}
    for pid in valid_product_ids:
        try:
            index = products_id.index(pid)
            product_name_map[pid] = products_name[index]
        except (IndexError, ValueError):
            product_name_map[pid] = "Not Found"

    return product_name_map


def get_user_subscription_history(user: User):
    """
    Get user subscription history
    """
    subscription_history = []

    if user.stripe_customer_id:
        subscriptions = stripe.Subscription.list(customer=user.stripe_customer_id, status='all')

        for sub in subscriptions['data']:
            product_id: str = sub['items']['data'][0]['price']['product']
            product_data: Dict = get_stripe_product_data_by_id(product_id)

            # Retrieve the latest invoice for the subscription
            invoices = stripe.Invoice.list(subscription=sub['id'], limit=1)
            latest_invoice = invoices.data[0] if invoices.data else None

            if latest_invoice:
                amount_paid = latest_invoice.amount_paid
            else:
                amount_paid = sub['items']['data'][0]['price']['unit_amount']

            subscription_history.append({
                    'active': sub['canceled_at'] is None,
                    'subscription_id': sub['id'],
                    'plan_name': product_data['name'],
                    'currency': sub['items']['data'][0]['price']['currency'],
                    'amount': amount_paid,
                    'created': datetime.datetime.fromtimestamp(
                        sub['created']
                    ).strftime("%d %b %Y"),
                    'created_date': datetime.datetime.fromtimestamp(
                        sub['created']
                    ).date(),
                    'current_period_start': datetime.datetime.fromtimestamp(
                        sub['current_period_start']
                    ).strftime("%d %b %Y"),
                    'current_period_end': datetime.datetime.fromtimestamp(
                        sub['current_period_end']
                    ).strftime("%d %b %Y"),
                })

    if user.appsumo_licenses.exists():
        for appsumo_license in user.appsumo_licenses.all():
            subscription_history.append({
                    'active': appsumo_license.license_status == "active",
                    'subscription_id': f"appsumo-ltd-id-{appsumo_license.id}",
                    'plan_name': f"Appsumo Tier {appsumo_license.tier}",
                    'currency': "usd",
                    # Stripe plans are priced in multiples of 100, so we need to multiply the price of Appsumo LTD plan by 100 as well.
                    'amount': appsumo_license.plan_amount_int * 100,
                    'created': appsumo_license.created_on.strftime("%d %b %Y"),
                    'created_date': appsumo_license.created_on.date(),
                    'current_period_start': None,
                    'current_period_end': None,
                })

    # Sort the subscription based on created date
    subscription_history = sorted(subscription_history, key=lambda subscriptoin: subscriptoin['created_date'], reverse=True)

    return subscription_history


def setup_free_plan_for_ltd_user(user_email: str, country: str):
    """
    Used to setup the free plan for LTD plan users
    :param user_email: User email
    :parma country: Country
    """
    # Fetch the user model instance
    try:
        user = User.objects.get(email=user_email)
    except User.DoesNotExist:
        logger.critical(f"No user found with '{user_email}' email")
        return None

    # Fetch the price id
    product_data = get_stripe_product_data_by_name("Trial")

    if country.lower() == "india":
        stripe_price_id = product_data["inr"]["id"]
    else:
        stripe_price_id = product_data["usd"]["id"]

    # Get stripe customer id by registering this user on stripe if not already present.
    if not user.stripe_customer_id:
        stripe_price = stripe.Price.retrieve(stripe_price_id)
        customer_creation_ik: str = generate_stripe_event_idempotency_key(
            "customer creation", user.email, user.password
        )

        if stripe_price.currency == "inr":
            customer = stripe.Customer.create(
                email=user.email,
                name=user.username,
                idempotency_key=customer_creation_ik,
                address={
                    "city": f"'{user.email}' city",
                    "country": "india",
                    "line1": f"'{user.email}' address line1",
                    "line2": f"'{user.email}' address line2",
                    "postal_code": f"'{user.email}' postal code",
                    "state": f"'{user.email}' state"
                }
            )
        else:
            customer = stripe.Customer.create(
                email=user.email,
                name=user.username,
                idempotency_key=customer_creation_ik
            )

        stripe_customer_id = customer['id']
        user.stripe_customer_id = stripe_customer_id
        user.save()

    else:
        stripe_customer_id = user.stripe_customer_id

    free_plan_subscription_ik = generate_stripe_event_idempotency_key(
        "free plan subscription",
        user.email,
        user.username
    )

    subscription = stripe.Subscription.create(
        customer=stripe_customer_id,
        items=[
            {'price': stripe_price_id}
        ],
        idempotency_key=free_plan_subscription_ik
    )

    user.stripe_subscription_id = subscription['id']
    user.stripe_pricing_id = subscription['items']['data'][0]['price']['id']
    user.stripe_product_id = subscription['items']['data'][0]['price']['product']
    user.save()
